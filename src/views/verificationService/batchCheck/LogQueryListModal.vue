<template>
  <BasicPopup v-bind="$attrs" class="transfer-modal member-modal" @register="registerPopup" title="日志" :footer="null">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'terminalName'">
          <!-- <Popover title="终端信息" placement="rightTop" trigger="click" @openChange="e => getDeviceByInfo(e, record.terminalId)">
            <template #content>
              <Descriptions :column="2" bordered :labelStyle="{ width: '120px' }" :contentStyle="{ width: '160px' }">
                <DescriptionsItem label="终端编号">{{ deviceParams.code || '' }}</DescriptionsItem>
                <DescriptionsItem label="终端序列号">{{ deviceParams?.terminalSn || deviceParams?.devcSn || deviceParams?.sn || '' }}</DescriptionsItem>
                <DescriptionsItem label="终端IP">{{ (deviceParams?.ip?.match(/(\d+\.\d+\.\d+\.\d+)/) || [])[0] || '' }}</DescriptionsItem>
                <DescriptionsItem label="终端型号">{{ deviceParams.deviType || '' }}</DescriptionsItem>
                <DescriptionsItem label="终端状态">
                  <a-tag :color="deviceParams.terminalOnline ? 'green' : 'red'">{{ deviceParams.terminalOnline ? '在线' : '离线' }}</a-tag>
                </DescriptionsItem>
                <DescriptionsItem label="终端所在地">{{ deviceParams.addr || '' }}</DescriptionsItem>
                <DescriptionsItem label="当前使用场景">{{ deviceParams.batchName || '' }}</DescriptionsItem>
                <DescriptionsItem label="终端名称">{{ deviceParams.name || '' }}</DescriptionsItem>
                <DescriptionsItem label="终端类型">{{ deviceParams.devcType || '' }}</DescriptionsItem>
                <DescriptionsItem label="归属应用">{{ deviceParams.moduleName || '' }}</DescriptionsItem>
                <DescriptionsItem label="是否启用">{{ deviceParams.isEnabled ? '启用' : '停用' || '' }}</DescriptionsItem>
                <DescriptionsItem label="参数模版">{{ deviceParams.parametersId || '' }}</DescriptionsItem>
                <DescriptionsItem label="人员记录数">{{ deviceParams.personCount || '' }}</DescriptionsItem>
              </Descriptions>
            </template>
            <a-button type="link">{{ record.terminalName }}</a-button>
          </Popover> -->
          {{ record.terminalName }}
        </template>
        <template v-if="column.dataIndex === 'sendStatus'">
          <template v-if="!record.sendStatus"></template>
          <a-tag v-else :color="SendStatusMap[record.sendStatus || SendStatusEnum.ERROR].color">
            {{ SendStatusMap[record.sendStatus || SendStatusEnum.ERROR].text }}
          </a-tag>
        </template>
        <template v-if="column.key === 'action'">
          <TableAction :actions="getTableActions(record)" />
        </template>
      </template>
    </BasicTable>
  </BasicPopup>

  <TerminalLogDetailForm @register="registerTerminalLogDetailForm" @reload="reload" />
  <TerminalLogDetailList @register="registerTerminalLogDetailList" />
  <TermManageDetail @register="registerTermManageDetail" @reload="reload" />
</template>

<script lang="ts" setup>
  import { sendParams as batchSendParams } from '@/api/term/batch';
  // import * as sendLogApi from '@/api/term/sendDetailLog';
  import * as sendLogApi from '@/api/term/sendLog';
  import * as terminalApi from '@/api/term/terminal';
  import * as recvLogApi from '@/api/term/recvLog';

  import { useModal } from '@/components/Modal';
  import { useDrawer } from '@/components/Drawer';
  import { BasicPopup, usePopup, usePopupInner } from '@/components/Popup';
  import { BasicColumn, BasicTable, TableAction, useTable } from '@/components/Table';
  import { useMessage } from '@/hooks/web/useMessage';
  import TerminalLogDetailForm from '@/views/smartTerminal/Log/TerminalLogDetailForm.vue';
  import TerminalLogDetailList from '@/views/smartTerminal/Log/TerminalLogDetailList.vue';
  import { Descriptions, DescriptionsItem, Popover, Tag } from 'ant-design-vue';
  import { reactive, ref } from 'vue';
  import { SendStatusMap, SendStatusEnum, SendStatusOptions, TerminalLogTypeOptions, TerminalLogType } from '@/enums/otherEnum';
  import TermManageDetail from '@/views/smartTerminal/TermManage/Detail.vue';

  const emit = defineEmits(['register', 'reload']);
  const { createMessage, createConfirm } = useMessage();
  const state = reactive({
    batchId: '',
  });
  const [registerPopup, { closePopup, changeLoading, changeOkLoading }] = usePopupInner(data => {
    state.batchId = data.id;
    reload();
  });

  const [registerTermManageDetail, { openPopup: openTermManageDetail }] = usePopup();

  const columns: BasicColumn[] = [
    {
      title: '终端名称',
      dataIndex: 'terminalName',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '终端编号',
      dataIndex: 'terminalCode',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '终端序列号',
      dataIndex: 'terminalSn',
      align: 'center',
      ellipsis: true,
      customRender: ({ record }) => {
        return record?.terminalSn || record?.devcSn || record?.sn;
      },
    },
    {
      title: '终端IP',
      // dataIndex: 'thIp',
      dataIndex: 'terminalThIp',
      align: 'center',
      ellipsis: true,
      // customRender: ({ record, text }) => {
      //   return text ? text.match(/(\d+\.\d+\.\d+\.\d+)/)[0] : '';
      // },
    },
    {
      title: '下发类型',
      dataIndex: 'sendType',
      align: 'center',
      ellipsis: true,
      customRender: ({ record }) => {
        return record?.sendType ? TerminalLogType[record.sendType.toUpperCase()] : '';
      },
    },
    // {
    //   title: '核验批次',
    //   dataIndex: 'sendBatchName',
    //   align: 'center',
    //   ellipsis: true,
    //   // customRender: ({ record }) => {
    //   //   const json = JSON.parse(record?.sendPersionText || '{}');
    //   //   return json?.batchName ? json?.batchName : '无';
    //   // },
    // },
    {
      title: '下发状态',
      dataIndex: 'sendStatus',
      align: 'center',
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'sendErrorMsg',
    },
    { title: '下发时间', dataIndex: 'creatorTime', width: 180, format: 'date|YYYY-MM-DD HH:mm:ss' },
  ];
  const [registerTerminalLogDetailForm, { openPopup: openTerminalLogDetailForm }] = usePopup();
  const [registerTerminalLogDetailList, { openPopup: openTerminalLogDetailList }] = usePopup();

  const [registerTable, { reload, getForm, setLoading }] = useTable({
    api: sendLogApi.getList,
    columns,
    immediate: true,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: [
        // {
        //   field: 'sendType',
        //   label: '下发类型',
        //   component: 'Select',
        //   componentProps: {
        //     options: TerminalLogTypeOptions,
        //     placeholder: '全部',
        //     submitOnPressEnter: true,
        //   },
        // },

        {
          field: 'sendStatus',
          label: '下发状态',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            options: SendStatusOptions,
          },
        },
        {
          field: 'timeRange',
          label: '下发时间',
          component: 'DateRange',
          componentProps: {
            format: 'YYYY-MM-DD',
            placeholder: ['开始时间', '结束时间'],
          },
        },
      ],
      fieldMapToTime: [['timeRange', ['startTime', 'endTime']]],
    },
    beforeFetch: params => {
      params.batchId = state.batchId;
      params.sendType = 'SENDPERSONS';
      return params;
    },

    showIndexColumn: true,
    rowSelection: { type: 'checkbox' },
    showTableSetting: false,
    tableSetting: false,
    clickToRowSelect: false,
    actionColumn: {
      width: 150,
      title: '操作',
      align: 'center',
      dataIndex: 'action',
    },
  });

  function getTableActions(record) {
    return [
      {
        ifShow: !['SENDDELETEPERSON', 'SENDDELETELOG', 'SENDDELETEALL'].includes(record?.sendType ? record?.sendType.toUpperCase() : ''),
        label: '详情',
        onClick: () => getDetail(record),
      },
    ];
  }

  function getDetail(record) {
    if (['SENDFACECODE'].includes(record?.sendType ? record?.sendType.toUpperCase() : '')) {
      openTerminalLogDetailForm(true, { ...record, type: 1, isSend: 1 });
    } else if (['SENDBATCH', 'SENDPARAMS'].includes(record?.sendType ? record?.sendType.toUpperCase() : '')) {
      const params = {
        type: record?.sendType ? record.sendType.toUpperCase() : '',
        logData: record,
      };
      openTermManageDetail(true, params);
    } else {
      openTerminalLogDetailList(true, record);
    }
  }

  function handleContinueGoto(item) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '您确定要继续下发吗?',
      onOk: async () => {
        const res = await batchSendParams({ batchId: item.batchId, terminalCodes: item.sn });
        createMessage.success(res.msg);
      },
    });
  }
  const deviceParams = ref({});
  const getDeviceByInfo = async (visible, terminalCode) => {
    if (!visible) return;
    setLoading(true);
    const { data } = await terminalApi.getTerminalByCode(terminalCode);
    deviceParams.value = data;
    setLoading(false);
  };
</script>
