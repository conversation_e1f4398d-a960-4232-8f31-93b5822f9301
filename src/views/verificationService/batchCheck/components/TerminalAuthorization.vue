<!--
 * @Description: 终端授权
 * @Autor: panmy
 * @Date: 2025-04-24 16:34:14
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-05 17:36:39
-->
<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <a-row class="mb-10px bg-white p-10px" justify="space-between">
        <a-col class="pl-50px">
          关键词
          <a-input class="ml-5px" v-model:value="state.keyword" placeholder="请输入终端编号或名称搜索" style="width: 300px" allow-clear />
          <a-button class="ml-30px" type="primary" @click="getDetail()">查询</a-button>
          <a-button class="ml-10px" @click="handleRest">重置</a-button>
        </a-col>
      </a-row>
      <div class="mtcn-content-wrapper-content echarts">
        <div class="flex pl-50px" v-if="shareInfo?.operationType != 2">
          <a-button @click="handleAllSelect" type="primary" class="ml-10px">全选</a-button>
          <a-button @click="handleBatchPerson" type="primary" class="ml-10px">批量设置</a-button>
          <a-button @click="handleBatchPush" type="primary" class="ml-10px">批量下发</a-button>
          <a-button @click="handleBatchClear" type="primary" class="ml-10px">批量清空人员</a-button>
        </div>
        <a-checkbox-group v-model:value="state.checkedList" class="w-full">
          <div class="device-list">
            <div v-for="(item, index) in deviceList" :key="item.id">
              <div
                :class="{
                  'device-item': true,
                  'device-success': item.issStatus == 1,
                  'device-warning': item.warning == 2,
                  'device-fail': item.issStatus == 0,
                }">
                <div class="lbl-g">
                  <div
                    class="flex justify-between items-center"
                    @click="
                      openStaffListModal(true, {
                        batchId: item.batchId,
                        terminalId: item.terminalId,
                        issStatus: item.issStatus,
                        online: !item.termTerminalVo.online,
                        operationType: shareInfo?.operationType,
                        title: '名单总数详情',
                      })
                    ">
                    <span>名单总数：{{ item.totalCount || 0 }}人</span>
                    <a-button type="link" size="small" class="cFFFFFF" preIcon="icon-ym icon-ym-nav-next"></a-button>
                  </div>
                </div>
                <div class="lbl-g">
                  <span>核验方式：{{ RECOTYPES[item.recoType] }}</span>
                </div>
                <!-- <div class="lbl-g">
                  <span>
                    下发状态：

                    <b
                      v-if="['1', '999'].includes((item.issStatus || '999').toString())"
                      :color="SendStatusMap[item.issStatus || SendStatusEnum.ERROR].color"
                      :style="{ color: stateColor[item.issStatus] }">
                      {{ SendStatusMap[item.issStatus || SendStatusEnum.ERROR].text }}
                    </b>

                    <a-tooltip v-else :title="item.issMsg" :color="stateColor[item.issStatus]">
                      <b :color="SendStatusMap[item.issStatus || SendStatusEnum.ERROR].color" :style="{ color: stateColor[item.issStatus] }">
                        {{ SendStatusMap[item.issStatus || SendStatusEnum.ERROR].text }}
                      </b>
                      &nbsp;
                      <i
                        v-if="!item.issStatus == 1"
                        class="icon-ym icon-ym-tip-filled"
                        :style="{
                          fontSize: '13px',
                          color: stateColor[item.issStatus],
                        }"></i>
                    </a-tooltip>
                  </span>
                </div> -->
                <div class="lbl-g">
                  <div
                    class="flex justify-between items-center"
                    @click="
                      openHeadcountModal(true, {
                        batchId: item.batchId,
                        terminalId: item.terminalId,
                        terminalSn: item.terminalSn,
                        issStatus: item.issStatus,
                        online: !item.termTerminalVo.online,
                        operationType: shareInfo?.operationType,
                        extendFieldCol: shareInfo?.extendFieldCol,
                        terminalCodes: [item.terminalSn],
                      })
                    ">
                    <span>下发人数：{{ item.sendCount || 0 }}人</span>
                    <a-button type="link" size="small" class="cFFFFFF" preIcon="icon-ym icon-ym-nav-next"></a-button>
                  </div>
                </div>
                <div class="lbl-g mb0">
                  <span>终端编号：{{ item.termTerminalVo.code }}</span>
                  <span>终端序列号：{{ item.termTerminalVo?.terminalSn || item.termTerminalVo?.devcSn || item.termTerminalVo?.sn || '' }}</span>
                  <span>终端名称：{{ item.termTerminalVo.name }}</span>
                  <span>终端IP：{{ item.termTerminalVo?.thIp || item.termTerminalVo?.terminalThIp || '' }}</span>
                  <span
                    >终端状态：
                    <div :class="['online', item.termTerminalVo.online ? 'online1' : 'online0']">
                      <i></i>
                      {{ item.termTerminalVo.online ? '在线' : '离线' }}
                    </div>
                  </span>
                </div>
                <div class="btn">
                  <a-button
                    type="link"
                    size="small"
                    @click="
                      selectPersonList({
                        terminalBatchIds: [item.id],
                        terminalIds: [item.terminalId.toString()],
                        batchId: stepIds[0],
                        extendFieldCol: shareInfo?.extendFieldCol,
                        terminalSn: [item.terminalSn],
                        operationType: 'single',
                      })
                    ">
                    设置人员
                  </a-button>
                  <div class="line"></div>
                  <a-button
                    :disabled="shareInfo?.operationType == 2 || !item.termTerminalVo.online ? true : false"
                    type="link"
                    size="small"
                    @click="handleSend([item.terminalSn])"
                    >全部下发</a-button
                  >

                  <div class="line"></div>
                  <a-button
                    :disabled="item.updateStatus != 2"
                    :style="{ color: item.updateStatus == 2 ? '#52c41a' : '#ffffff' }"
                    @click="handleUpdateParamsAndPerson([item.terminalSn])"
                    type="link"
                    size="small">
                    更新人员
                  </a-button>
                </div>
              </div>
              <div class="device-cb">
                <a-checkbox :value="item.terminalId" :disabled="!item.termTerminalVo.online">选择</a-checkbox>
              </div>
            </div>
          </div>
        </a-checkbox-group>
      </div>
    </div>
  </div>
  <StaffListModal @register="registerStaffListModal" />
  <HeadcountModal @register="registerHeadcountModal" />
</template>

<script lang="ts" setup>
    import { sendParams as batchSendParams, sendParamsAndPerson } from '@/api/term/batch';
    import * as batchRelationApi from '@/api/term/batchRelation';
    import * as batchTerminalApi from '@/api/term/batchTerminal';
    import {updateParamsAndPerson} from '@/api/term/batch';
    import * as terminalApi from '@/api/term/terminal';
    import { useDrawer } from '@/components/Drawer';
    import { useMessage } from '@/hooks/web/useMessage';
    import { eventBus } from '@/utils/eventBus';
    import { Badge as ABadge} from 'ant-design-vue';
    import { onMounted, onUnmounted, reactive, ref } from 'vue';
    import StaffListModal from './StaffListModal.vue';
    import HeadcountModal from './HeadcountModal.vue';
    import { SendStatusMap, SendStatusEnum, TerminalLogTypeOptions, TerminalLogType, HCStatusEnum, HCStatusMap,RECOTYPES } from '@/enums/otherEnum';


    const props = defineProps({
      stepIds: { type: Array, default: [] },
      stepItems: { type: Array, default: [] },
      shareInfo: { type: Object, default: () => ({}) },
      actions: { type: Object, default: () => ({}) },
    });
    const { createMessage, createConfirm } = useMessage();

    const [registerStaffListModal, { openDrawer: openStaffListModal }] = useDrawer();
    const [registerHeadcountModal, { openDrawer: openHeadcountModal }] = useDrawer();

    const stateColor = { '1': '#52c41a', '0': '#ff4d4f', '2': '#ff4d4f', '4': '#faad14' };
    const state = reactive({
      keyword: '',
      checkAll: false,
      checkedList: [],
    });

    const handleRest = () => {
      state.keyword = '';
      state.checkedList =[]
    };

    function handleAllSelect() {
      state.checkAll = !state.checkAll;
      state.checkedList = state.checkAll ? deviceList.value.map(item =>
      {
       if(item.termTerminalVo.online) {return item.terminalId}
      })
       : [];
    }

    function selectPersonList(obj) {
      props.actions.terminalAuthorization.openPersonList(obj);
    }

    async function handleBatchPerson() {
      const terminalIds = state.checkedList.map(item => item.toString());
      if (terminalIds.length === 0) return createMessage.error('请选择终端');

      const ids = deviceList.value.map(item => {
        if (state.checkedList.includes(item.terminalId)) return item.id.toString();
      });

      selectPersonList({
        terminalBatchIds: ids,
        terminalIds: terminalIds,
        batchId: props.stepIds[0],
        extendFieldCol: [],
         operationType: 'multiple'
      });
    }
    function handleBatchClear() {
      if (state.checkedList.length === 0) return createMessage.error('请选择终端');
      createConfirm({
        title: '',
        content: '确定要清空人员吗？',
        onOk: async () => {
          const data = [];
          state.checkedList.forEach(item =>
            data.push({
              terminalIds: [item.toString()],
              batchId: props.stepIds[0],
              personSettingType: 'ALL',
            }),
          );
          await batchRelationApi.clearByBatchId(data);
          createMessage.success('清空成功');
          getDetail();
        },
      });
    }
    function handleRemove(id) {
      createConfirm({
        title: '',
        content: '确定要清空人员吗？',
        onOk: async () => {
          await batchRelationApi.clearByBatchId([{ terminalIds: [id.toString()], batchId: props.stepIds[0] }]);
          createMessage.success('清空成功');
          getDetail();
        },
      });
    }

    // 添加 save 方法 type:0:保存 1:保存并下发
    const save = async type => {
      try {
        if (type == 1) {
          const terminalCodes = [];
          deviceList.value.forEach(item => {
            terminalCodes.push(item.terminalSn);
          });
          if (terminalCodes.length == 0) throw new Error('请在"终端设置"选择终端数据');
          // 保存并下发
          await sendParamsAndPerson({ batchId: props.stepIds[0], terminalCodes: terminalCodes });
        }
        return {
          success: true,
          id: '',
          errorMessage: '',
        };
      } catch (error) {
        return {
          success: false,
          id: '',
          errorMessage: '保存失败',
        };
      }
    };

    const handleBatchPush = async () => {
      const ids = state.checkedList.map(item => item.toString());
      if (ids.length === 0) return createMessage.error('请选择终端');
      const terminalCodes = deviceList.value.map(item => {
        if (ids.includes(item.id)) return item.terminalSn.toString();
      });
      createConfirm({
        title: '',
        content: '确定要批量下发吗？',
        onOk: async () => {
          const res = await sendParamsAndPerson({ batchId: props.stepIds[0], terminalCodes: terminalCodes });
          createMessage.success(res.msg);
          getDetail();
        },
      });
    };

    // 新增 getDetail 方法
    const deviceList = ref([]);
    const getDetail = async () => {
      const { data } = await batchTerminalApi.getListAll({ batchId: props.stepIds[0] });
      deviceList.value = data;
    };

    const handleSend = terminalCodes => {
      createConfirm({
        title: '',
        content: '您确定要下发吗？',
        onOk: async () => {
          const res = await sendParamsAndPerson({ batchId: props.stepIds[0], terminalCodes });
          createMessage.success(res.msg);
          getDetail();
        },
      });
    };
    // 更新
      const handleUpdateParamsAndPerson = async (sn) => {
      createConfirm({
        title: '',
        content: '您确定要更新吗？',
        onOk: async () => {
          const res = await updateParamsAndPerson({ batchId: props.stepIds[0], terminalCodes:sn });
          createMessage.success(res.msg);
          getDetail();
        },
      });
  }

  function getTerminalState(){
   terminalApi.getListAll().then((res) => {
     const terminalRes = res.data;
     deviceList.value.map((item) => {
       item.termTerminalVo.online = terminalRes.find((item1) => item1.code === item.terminalCode)?.online;
     });
   })
  }

  const timerId = ref(null);
    function handleAutomaticRetrieval‌() {
      if (timerId.value) {
        clearInterval(timerId.value);
      }

      timerId.value = setInterval(() => {
         getDetail()
      }, 3000);
    }



    onMounted(() => {
     handleAutomaticRetrieval‌()
      eventBus.on('refreshDetail', () => {
        getDetail();
      });
    });

    onUnmounted(() => {
      eventBus.off('refreshDetail');
      if (timerId.value) {
        clearInterval(timerId.value);
        timerId.value = null;
      }
    });
   const checkValidate = async () => {
     return {
         success: true,
       };
   };
   // 暴露 save 方法给父组件
   defineExpose({ save, getDetail, checkValidate });
</script>
<style lang="less" scoped>
  .device-list {
    width: 100%;
    padding: 20px 5% 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, 230px);
    gap: 10px 20px;
    justify-content: center;
    .device-item {
      height: 400px;
      padding: 60px 10px;
      box-sizing: border-box;
      background: url('./images/device-bg.png') no-repeat 0 0 / 100% 100%;
      border: 3px solid transparent;
      position: relative;
      .lbl-g {
        line-height: 20px;
        padding: 4px;
        background: linear-gradient(135deg, #6782ff 100%);
        color: white;
        border-radius: 5px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        margin-bottom: 10px;
        display: flex;
        flex-direction: column;
        &:last-of-type {
          margin-bottom: 5px;
        }
        span {
          display: flex;
          align-items: center;
          font-size: 13px;
          color: #fff;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
      .btn {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 47px;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        margin-top: 10px;
        .line {
          width: 1px;
          height: 12px;
          margin: 0 0px;
          background-color: #ffffff;
          flex-shrink: 0;
        }
        :deep(.ant-btn) {
          color: #ffffff;
          font-size: 13px;
          padding: 0;
        }
      }
    }

    .device-success {
      border: 3px solid #52c41a;
      border-radius: 13px;
    }
    .device-fail {
      border: 3px solid #ed6f6f;
      border-radius: 13px;
    }
    .device-warning {
      border: 3px solid #faad14;
      border-radius: 13px;
    }
    .device-cb {
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .online {
      display: flex;
      align-items: center;

      i {
        position: relative;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-right: 3px;
      }
    }
    .online0 {
      color: #ed6f6f;
      i {
        background: #ed6f6f;
        &:after {
          position: absolute;
          top: 0;
          inset-inline-start: 0;
          width: 100%;
          height: 100%;
          border-width: 1px;
          border-style: solid;
          border-color: inherit;
          border-radius: 50%;
          animation-name: stateAni;
          animation-duration: 1.2s;
          animation-iteration-count: infinite;
          animation-timing-function: ease-in-out;
          content: '';
          @keyframes stateAni {
            0% {
              transform: scale(1);
              opacity: 0.5;
            }
            100% {
              transform: scale(4.4);
              opacity: 0;
            }
          }
        }
      }
    }
    .online1 {
      color: #52c41a;
      i {
        background: #52c41a;
      }
    }

    .cFFFFFF {
      color: #ffffff;
    }
  }
</style>
