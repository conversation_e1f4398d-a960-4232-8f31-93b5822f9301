<!--
 * @Description: 下发人数名单
 * @Autor: panmy
 * @Date: 2025-04-14 11:34:17
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-05 18:05:39
-->
<template>
  <BasicDrawer
    v-bind="$attrs"
    width="1000px"
    class="full-drawer"
    style="height: 100vh"
    @register="registerDrawer"
    title="下发人数名单详情"
    :width="1000"
    :footer="null">
    <a-tabs v-model:activeKey="activeKey" @change="handleTabsChange">
      <a-tab-pane key="1">
        <template #tab>
          <span class="pl-10px">下发成功人员</span>
        </template>
      </a-tab-pane>
      <a-tab-pane key="0" tab="下发失败人员"> </a-tab-pane>
      <!-- <a-tab-pane key="0" tab="未下发人员"></a-tab-pane> -->
    </a-tabs>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button v-if="state.operationType != 0 && activeKey == 0" type="primary" preIcon="icon-ym icon-ym-undo" @click="continueSend">重新下发</a-button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'sendStatus'">
          <!-- <a-tag :color="{ '0': 'warning', '1': 'success', '2': 'error' }[record.sendStatus]">
            {{ record.sendStatus }}
          </a-tag> -->
          <a-tag :color="SendStatusMap[record.sendStatus || SendStatusEnum.ERROR].color">
            {{ SendStatusMap[record.sendStatus || SendStatusEnum.ERROR].text }}
          </a-tag>
        </template>
      </template>
    </BasicTable>
  </BasicDrawer>
</template>

<script lang="ts" setup>
  import { reactive, ref, nextTick } from 'vue';
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { BasicColumn, BasicTable, TableAction, useTable } from '@/components/Table';
  import * as batchRelationApi from '@/api/term/batchRelation';
  import * as sendDetailLogApi from '@/api/term/sendDetailLog';
  import { sendPersons } from '@/api/term/batch';
  import { usePopup } from '@/components/Popup';
  import dayjs from 'dayjs';
  import { useMessage } from '@/hooks/web/useMessage';
  import { SendStatusMap, SendStatusEnum, SendStatusOptions, TerminalLogTypeOptions, TerminalLogType } from '@/enums/otherEnum';

  const emit = defineEmits(['register', 'reload', 'openDetailPopup']);
  const activeKey = ref('1');
  const state = reactive({
    operationType: '',
    terminalId: '',
    batchId: '',
    extendFieldCol: [],
    issStatus: '',
    online: '',
    terminalCodes: '',
  });
  const [registerDrawer, { closeModal }] = useDrawerInner(data => {
    state.batchId = data?.batchId || undefined;
    state.terminalId = data?.terminalId || undefined;
    state.terminalSn = data?.terminalSn || undefined;

    state.operationType = data?.operationType || 2;
    //  state.extendFieldCol =
    // (data?.extendFieldCol || []).map(item => {
    //   return {
    //     ...item,
    //     customRender: ({ record }) => {
    //       return record?.sendPersionText?[item.dataIndex] || '';
    //     }
    //   }
    // }) || [];
    state.issStatus = data?.issStatus || undefined;
    state.online = data?.online || undefined;
    state.terminalCodes = data?.terminalCodes || undefined;
    activeKey.value = '1';
    setCurrentColumns();
    nextTick(() => {
      clearSelectedRowKeys();
      reload();
    });
  });

  const { createMessage, createConfirm } = useMessage();
  const columns: BasicColumn[] = [
    // {
    //   title: '已设置终端',
    //   dataIndex: 'terminals',
    //   width: 150,
    //   resizable: true,
    //   ellipsis: true,
    // },
  ];

  const [registerTable, { reload, getSelectRows, getSelectRowKeys, clearSelectedRowKeys, getDataSource, setColumns, setLoading }] = useTable({
    // api: batchRelationApi.getList,
    api: sendDetailLogApi.getList,
    columns,
    immediate: false,
    useSearchForm: true,
    rowSelection: {
      type: 'checkbox',
    },
    formConfig: {
      labelWidth: 100,
      schemas: [
        {
          field: 'keyword',
          label: '关键字',
          component: 'Input',
          componentProps: { placeholder: '请输入人员编号、姓名搜索' },
        },
        {
          field: 'psnType',
          label: '人员类型',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            allowClear: false,
            options: [
              { fullName: '教职工', id: 'TEACHER' },
              { fullName: '学生', id: 'STUDENT' },
              { fullName: '临时人员', id: 'TEMPORARY' },
            ],
            onChange: (val, obj) => {
              reload();
            },
          },
        },
      ],
    },
    beforeFetch: async (params = {}) => {
      return {
        ...params,
        online: state.online,
        terminalId: state.terminalId,
        terminalSn: state.terminalSn,
        batchId: state.batchId,
        // issStatus: activeKey.value,
        sendStatus: activeKey.value,
        sendType: 'SENDPERSONS',
      };
    },
    afterFetch: list => {
      const data = list.map(item => {
        return {
          ...item,
          sendPersionText: JSON.parse(item.sendPersionText || '{}'),
        };
      });
      return data;
    },
    showIndexColumn: true,
    clickToRowSelect: false,
  });

  function continueSend(record) {
    const personIds = (getSelectRows() || []).map(item => item.psnBh);
    createConfirm({
      title: '',
      content: '您确定要重新下发吗？',
      onOk: async () => {
        setLoading(true);
        const res = await sendPersons({ batchId: state.batchId, terminalCodes: state.terminalCodes, personIds });
        setTimeout(() => {
          createMessage.success(res.msg);
          reload();
          clearSelectedRowKeys();
          setLoading(false);
        }, 5 * 1000);
      },
    });
    // 实现继续下发逻辑
  }

  function setCurrentColumns() {
    const column = [
      {
        title: '人员编号',
        dataIndex: 'psnBh',
        width: 140,
        resizable: true,
        ellipsis: true,
        fixed: 'left',
        customRender: ({ record }) => {
          return record?.sendPersionText?.psnBh || '';
        },
      },
      {
        title: '人员类型',
        dataIndex: 'psnTypeName',
        width: 100,
        resizable: true,
        ellipsis: true,
      },
      {
        title: '姓名',
        dataIndex: 'persionPsnName',
        width: 100,
        fixed: 'left',
        resizable: true,
        ellipsis: true,
        // customRender: ({ record }) => {
        //   return record?.termPersonVo?.psnName || '';
        // },
      },
      {
        title: '性别',
        dataIndex: 'xbmc',
        width: 60,
        resizable: true,
        ellipsis: true,
        customRender: ({ record }) => {
          return record?.sendPersionText?.xbmc || '';
        },
      },
      {
        title: '证件号码',
        dataIndex: 'persionSfzjh',
        width: 160,
        resizable: true,
        ellipsis: true,
        // customRender: ({ record }) => {
        //   return record?.termPersonVo?.sfzjh || '';
        // },
      },
      {
        title: '手机号码',
        dataIndex: 'persionMobile',
        width: 150,
        resizable: true,
        ellipsis: true,
        // customRender: ({ record }) => {
        //   return record?.termPersonVo?.mobile || '';
        // },
      },
      {
        title: '所属组织',
        dataIndex: 'dwmc',
        width: 100,
        resizable: true,
        ellipsis: true,
        customRender: ({ record }) => {
          return record?.sendPersionText?.dwmc || '';
        },
      },
      {
        title: '年级',
        dataIndex: 'njdm',
        width: 80,
        resizable: true,
        ellipsis: true,
        defaultHidden: true,
        customRender: ({ record }) => {
          return record?.sendPersionText?.njdm || '';
        },
      },
      {
        title: '专业',
        dataIndex: 'zymc',
        width: 100,
        resizable: true,
        defaultHidden: true,
        ellipsis: true,
        customRender: ({ record }) => {
          return record?.sendPersionText?.zymc || '';
        },
      },
      {
        title: '班级',
        dataIndex: 'bjmc',
        width: 80,
        resizable: true,
        defaultHidden: true,
        ellipsis: true,
        customRender: ({ record }) => {
          return record?.sendPersionText?.bjmc || '';
        },
      },

      ...state?.extendFieldCol,
      {
        title: '备注',
        dataIndex: 'sendErrorMsg', // 'issMsg',
      },
    ];
    setColumns(column);
  }
  const handleTabsChange = key => {
    reload();
  };
</script>
