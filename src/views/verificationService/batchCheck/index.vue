<!--
 * @Description: 核验批次
 * @Autor: panmy
 * @Date: 2024-05-15 18:35:05
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-05 17:06:46
-->
<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <a-row class="mb-10px bg-white p-10px" justify="space-between">
        <a-col>
          关键词 <a-input class="ml-10px w-300px" v-model:value="state.keyword" placeholder="请输入批次名称搜索" allow-clear @keyup.enter="loadList()" />
          <a-button class="ml-30px" type="primary" @click="loadList()">查询</a-button>
          <a-button
            class="ml-10px"
            @click="
              () => {
                state.keyword = '';
                loadList();
              }
            "
            >重置</a-button
          >
        </a-col>
        <a-col> </a-col>
      </a-row>
      <div class="mtcn-content-wrapper-content">
        <ScrollContainer>
          <Spin :spinning="loading" size="large">
            <div class="portalMain">
              <div :class="['card add']" @click="openFormPopup(true, { id: '', operationType: 0 })">
                <div> <i class="icon-ym icon-ym-btn-add"></i></div>
                <div>新建批次</div>
              </div>
              <template v-if="portalList.length > 0">
                <template v-for="(item, index) in portalList" :key="item.id">
                  <div v-show="shouldShow(item.status)">
                    <div
                      :class="['card hover:shadow-lg', item.status == 1 ? 'cardSel1' : 'cardSel0']"
                      :style="`background:url(${gradients[index % 8]})  no-repeat 0 0/100% 100%`"
                      @click="openFormPopup(true, { ...item, operationType: 2 })">
                      <div class="title ellipsis">{{ item?.batchName }}</div>
                      <div class="body">
                        <div>开始时间</div>
                        <div>{{ dayjs(item?.recoStartTime).format('YYYY-MM-DD') }}</div>
                        <div>结束时间</div>
                        <div>{{ dayjs(item?.recoEndTime).format('YYYY-MM-DD') }}</div>
                        <div>核验方式</div>
                        <div>{{ item?.recoType }}</div>
                        <div>核验人数</div>
                        <div>{{ item?.psnCount || 0 }}人</div>
                      </div>
                      <div class="bottom">
                        <a @click.stop="openFormPopup(true, { ...item, operationType: 1 })">编辑</a>
                        <div>｜</div>
                        <a v-if="item?.screenId" @click.stop="openWindow(`${globSetting.dataVUrl}View/${item.screenId}?token=${getToken()}`)">大屏</a>
                        <a v-else @click.stop="openLogQueryListPopup(true, { id: item.id })">日志</a>
                        <div>｜</div>
                        <a-dropdown>
                          <a class="link flex items-center" @click.stop.prevent>
                            更多
                            <DownOutlined class="ml-5px" />
                          </a>
                          <template #overlay>
                            <a-menu>
                              <a-menu-item v-if="item?.screenId" @click.stop="openLogQueryListPopup(true, { id: item.id })">日志 </a-menu-item>
                              <a-menu-item @click.stop="deletePc(item)" style="color: #ed6f6f"> 删除 </a-menu-item>
                            </a-menu>
                          </template>
                        </a-dropdown>
                      </div>
                    </div>
                  </div>
                </template>
              </template>
            </div>
            <a-button
              class="btn-history"
              type="primary"
              :postIcon="state.isShow ? 'icon-ym icon-ym-btn-collapse' : ' icon-ym icon-ym-btn-expand'"
              @click="() => (state.isShow = !state.isShow)"
              >历史批次</a-button
            >
          </Spin>
          <!-- <Empty class="empty" :image="simpleImage" v-else /> -->
        </ScrollContainer>
      </div>
    </div>
    <FormPopup @register="registerFormPopup" @reload="loadList" @openPersonList="e => openAddPersonListPopup(true, e)" />
    <LogQueryListModal @register="registerLogQueryListPopup" @reload="loadList" />

    <AddPersonListPopup @register="registerAddPersonListPopup" />
  </div>
</template>
<script lang="ts" setup>
  import { ref, computed, reactive, toRefs, nextTick, watch, unref, onMounted } from 'vue';
  import { useRoute } from 'vue-router';
  import { BasicPopup, usePopup, usePopupInner } from '@/components/Popup';
  import { useMessage } from '@/hooks/web/useMessage';
  import { DownOutlined } from '@ant-design/icons-vue';
  import { ScrollContainer } from '@/components/Container';
  import { BasicForm, useForm, FormSchema } from '@/components/Form';
  import { useI18n } from '@/hooks/web/useI18n';
  import { getMenuList, delMenu, exportMenu, getMenuSelector } from '@/api/system/menu';
  import { useModal } from '@/components/Modal';
  import { BasicModal } from '@/components/Modal';
  import * as termBatchApi from '@/api/term/batch';
  import { Spin, Empty } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import FormPopup from './FormPopup.vue';
  import LogQueryListModal from './LogQueryListModal.vue';
  import { openWindow } from '@/utils';
  import { getToken } from '@/utils/auth';
  import { useGlobSetting } from '@/hooks/setting';
  import AddPersonListPopup from './AddPersonListPopup.vue';
  import pcbj_01 from '@/assets/images/batchCheck/pcbj_01.png';
  import pcbj_02 from '@/assets/images/batchCheck/pcbj_02.png';
  import pcbj_03 from '@/assets/images/batchCheck/pcbj_03.png';
  import pcbj_04 from '@/assets/images/batchCheck/pcbj_04.png';
  import pcbj_05 from '@/assets/images/batchCheck/pcbj_05.png';
  import pcbj_06 from '@/assets/images/batchCheck/pcbj_06.png';
  import pcbj_07 from '@/assets/images/batchCheck/pcbj_07.png';
  import pcbj_08 from '@/assets/images/batchCheck/pcbj_08.png';
  import { RECOTYPES } from '@/enums/otherEnum';

  const route = useRoute();
  const globSetting = useGlobSetting();
  const simpleImage = Empty.PRESENTED_IMAGE_SIMPLE;
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();
  const state = reactive({
    systemId: '',
    title: '',
    parentId: '',
    isDevPlatform: false,
    keyword: '',
    isShow: false,
  });
  const { listQuery, title, systemId, parentId } = toRefs(state);
  const shouldShow = status => {
    return status == 1 ? true : state.isShow;
  };
  const [registerFormPopup, { openPopup: openFormPopup }] = usePopup();
  const [registerForm, { openModal: openFormModal }] = useModal();
  const [registerLogQueryListPopup, { openPopup: openLogQueryListPopup }] = usePopup();
  // 人员
  const [registerAddPersonListPopup, { openPopup: openAddPersonListPopup }] = usePopup();

  const gradients = [pcbj_01, pcbj_02, pcbj_03, pcbj_04, pcbj_05, pcbj_06, pcbj_07, pcbj_08];
  const loading = ref(false);
  const portalList = ref([]);
  async function loadList() {
    loading.value = true;
    const res = await termBatchApi.getListAll({ batchName: state.keyword });
    portalList.value = res.data;
    loading.value = false;
  }
  function updateStatus(obj) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: `确定修改为${obj.enabledMark == 1 ? '停用' : '启用'}吗?`,
      onOk: () => {
        termBatchApi.edit({ id: obj.id, enabledMark: obj.enabledMark == 1 ? 0 : 1 }).then(res => {
          createMessage.success(res.msg);
          loadList();
        });
      },
    });
  }

  function deletePc(obj) {
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '确定删除吗?',
      onOk: () => {
        termBatchApi.remove(obj.id).then(res => {
          createMessage.success(res.msg);
          loadList();
        });
      },
    });
  }
  onMounted(() => {
    const id = route?.query?.id || undefined;
    if (id) {
      openFormPopup(true, { id: id == 'add' ? '' : id, operationType: id == 'add' ? 0 : 1 });
    }

    loadList();
  });
</script>

<style lang="less" scoped>
  .mtcn-content-wrapper-content {
    background: #fff;
    position: relative;
    .empty {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .portalMain {
    display: grid;
    grid-template-columns: repeat(auto-fill, 220px);
    grid-auto-rows: minmax(240px, auto);
    padding: 10px;
    margin: 10px;
    grid-gap: 12px;
    .card {
      width: 100%;
      height: 240px;

      box-sizing: border-box;
      overflow: hidden;
      position: relative;
      padding: 30px 20px;
      border-radius: 3px;

      &:hover {
        .bottom {
          bottom: 0;
        }
      }

      .title {
        width: 100%;
        display: inline-block;
        font-size: 16px;
        font-weight: 700;
        margin-bottom: 10px;
        color: #ffffff;
      }
      .ellipsis {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .body {
        .ellipsis();
        display: grid;
        grid-template-columns: repeat(2, 80px);
        grid-gap: 10px 0px;
        font-size: 12px;
        color: #ffffff;
      }

      .bottom {
        transition-duration: 0.25s;
        position: absolute;
        bottom: -50px;
        left: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 46px;
        font-size: 17px;
        background-color: rgba(0, 0, 0, 0.7);
        color: #ffffff;
        cursor: pointer;
        a {
          color: #ffffff;
          font-size: 14px;
          cursor: pointer;
          display: block;
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
    .cardSel0::after {
      content: '停用';
      top: 10px;
      right: -20px;
      width: 80px;
      height: 20px;
      line-height: 20px;
      position: absolute;
      transform: rotate(45deg);
      font-size: 12px;
      text-align: center;
      color: #fff;
      background: rgb(255, 85, 0);
    }
    .cardSel1::after {
      content: '启用';
      top: 10px;
      right: -20px;
      width: 80px;
      height: 20px;
      line-height: 20px;
      position: absolute;
      transform: rotate(45deg);
      font-size: 12px;
      text-align: center;
      color: #fff;
      background: #00b42a;
    }
    .add {
      background: #f1f1f7;
      border: 1px dashed #b2b2b2;
      box-shadow: 0 0 0px #3e50b4;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      font-size: 14px;
      color: #aaaaaa;
      cursor: pointer;
      transition-duration: 0.25s;
      &:hover {
        color: @primary-color;
        border: 1px dashed @primary-color;
      }
      i {
        width: 40px;
        height: 4px;
        font: 30px;
      }
    }
  }
  .ml30 {
    margin-left: 30px;
  }
  .btn-history {
    position: fixed;
    right: 10px;
    top: 50%;
    // background-color: white;
    border-radius: 20px 0 0 20px;
  }
</style>
