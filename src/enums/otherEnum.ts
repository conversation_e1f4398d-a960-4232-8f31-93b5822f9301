/*
 * @Description:
 * @Autor: panmy
 * @Date: 2025-06-24 09:59:32
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-05 16:58:24
 */
export enum SendStatusEnum {
  SUCCESS = '1',
  FAIL = '0',
  ERROR = '2',
  SENDING = '4',
}

// 下发状态
export const SendStatusMap = {
  [SendStatusEnum.SUCCESS]: { text: '成功', color: 'success' },
  [SendStatusEnum.FAIL]: { text: '失败', color: 'error' },
  [SendStatusEnum.ERROR]: { text: '错误', color: 'warning' },
  [SendStatusEnum.SENDING]: { text: '下发中', color: 'warning' },
};

export const SendStatusOptions = [
  { label: '成功', fullName: '成功', value: '1', id: '1' },
  { label: '失败', fullName: '失败', value: '0', id: '0' },
  { label: '错误', fullName: '错误', value: '2', id: '2' },
  { label: '下发中', fullName: '下发中', value: '4', id: '4' },
];

// 下发类型
export enum TerminalLogType {
  SENDFACECODE = '特征合法性校验',
  SENDPARAMS = '终端参数下发',
  SENDBATCH = '批次参数下发',
  SENDDELETEPERSON = '删除人员数据下发',
  SENDDELETELOG = '删除日志数据下发',
  SENDDELETEALL = '删除全部数据下发',
  SENDPERSONS = '人员数据下发',
  SENDDELETEPARTPERSON = '更新人员下发',
}

export const TerminalLogTypeOptions = Object.keys(TerminalLogType).map(key => ({
  label: TerminalLogType[key],
  value: key,
  fullName: TerminalLogType[key],
  id: key,
}));

export enum HCStatusEnum {
  SUCCESS = '1',
  FAIL = '0',
  ERROR = '2',
}

// 回传状态
export const HCStatusMap = {
  [HCStatusEnum.SUCCESS]: { text: '成功', color: 'success' },
  [HCStatusEnum.FAIL]: { text: '失败', color: 'error' },
  [HCStatusEnum.ERROR]: { text: '暂存', color: 'warning' },
};

export enum inftCodeType {
  VERIFICATION_RESULT = '核验结果',
  FEATURE_CODE = '特征合法性校验',
  HEARTBEAT = '心跳',
}

export const inftCodeTypeOptions = Object.keys(inftCodeType).map(key => ({
  label: inftCodeType[key],
  value: key,
  fullName: inftCodeType[key],
  id: key,
}));

// 核验方式
export const RECOTYPES = {
  '1': '身份证',
  '2': '人证',
  '3': '人脸',
  '4': '人证照',
  '5': '人工核验',
};

// 核验类型
export enum HYTypeMap {
  '1' = '人工核验',
  '0' = '自助核验',
  '2' = '未核验',
}

// 核验结果、核验状态
export enum HYStatusEnum {
  SUCCESS = '1',
  FAIL = '0',
  ERROR = '2',
}

// 核验状态
export const HYStatusMap = {
  [HYStatusEnum.SUCCESS]: { text: '核验成功', color: 'success' },
  [HYStatusEnum.FAIL]: { text: '未核验', color: 'error' },
  [HYStatusEnum.ERROR]: { text: '核验失败', color: 'warning' },
};

// 证件确认
export enum ZJQRStatusEnum {
  '1' = '已确认',
  '0' = '未确认',
}

// 人员类型
export enum PersonTypeEnum {
  'TEACHER' = '教职工',
  'STUDENT' = '学生',
  'TEMPORARY' = '临时人员',
}
